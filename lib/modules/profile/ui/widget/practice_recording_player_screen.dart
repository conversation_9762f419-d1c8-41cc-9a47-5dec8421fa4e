import 'package:flutter/material.dart';
import 'dart:async';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_bottom_nav_bar/melo_bottom_nav_bar.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:auto_route/auto_route.dart';

class PracticeRecordingPlayerBody extends StatelessWidget {
  final RecordingModel recording;
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final String? userProfileImageUrl;
  final String username;

  const PracticeRecordingPlayerBody({
    super.key,
    required this.recording,
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
    required this.isPlaying,
    required this.onPlayPause,
    this.userProfileImageUrl,
    required this.username,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header section with profile info
        Padding(
          padding: const EdgeInsets.fromLTRB(30.0, 20.0, 24.0, 24.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile image stack
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Image.asset(
                          'assets/png/profile_border.png',
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                        ClipOval(
                          child: SizedBox(
                            width: 68,
                            height: 68,
                            child: userProfileImageUrl != null && userProfileImageUrl!.isNotEmpty
                                ? ImageLoader.network(
                                    userProfileImageUrl!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        color: Colors.grey[800],
                                        child: const Icon(Icons.person, color: Colors.white, size: 48),
                                      );
                                    },
                                  )
                                : Container(
                                    color: Colors.grey[800],
                                    child: const Icon(Icons.person, color: Colors.white, size: 48),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(4.0),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/png/cat_complete.png',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 24),
              // Recording info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      recording.title,
                      style: const TextStyle(
                        fontFamily: 'Ethnocentric',
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      username,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 40),

        // Wave video player with play/pause overlay
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6.0),
          child: WaveGifPlayer(
            isPlaying: isPlaying,
            onPlayPause: onPlayPause,
          ),
        ),

        // Lyrics section
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: _LyricsDisplay(
              lyrics: lyrics,
              lyricsData: lyricsData,
              playbackPositionMillis: playbackPositionMillis,
              audioDurationMillis: audioDurationMillis,
            ),
          ),
        ),
      ],
    );
  }
}

class PracticeRecordingPlayerScreen extends StatefulWidget {
  final RecordingModel recording;
  const PracticeRecordingPlayerScreen({
    super.key,
    required this.recording,
  });

  @override
  State<PracticeRecordingPlayerScreen> createState() => _PracticeRecordingPlayerScreenState();
}

class _PracticeRecordingPlayerScreenState extends State<PracticeRecordingPlayerScreen> {
  late AudioPlayer _audioPlayer;
  int _playbackPositionMillis = 0;
  int _audioDurationMillis = 0;
  bool _isPlaying = false;
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  @override
  void initState() {
    super.initState();
    _initializeAudio();
    _fetchLyrics();
  }

  Future<void> _initializeAudio() async {
    _audioPlayer = AudioPlayer();

    // Set looping mode
    await _audioPlayer.setLoopMode(LoopMode.one);

    // Load and auto-play audio
    await _audioPlayer.setUrl(widget.recording.finalMixedAudioPath);

    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        logger.d('Audio player state changed: playing=${state.playing}, processingState=${state.processingState}');
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _playbackPositionMillis = position.inMilliseconds;
        });
      }
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      if (mounted && duration != null) {
        setState(() {
          _audioDurationMillis = duration.inMilliseconds;
        });
      }
    });

    // Start playing
    unawaited(_audioPlayer.play());
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    try {
      if (widget.recording.lyricsJsonPath.isNotEmpty) {
        final response = await DI().resolve<ApiClient>().get(widget.recording.lyricsJsonPath);
        if (response != null) {
          final lyricsData = LyricsData.fromJson(response);
          if (mounted) {
            setState(() {
              _lyricsData = lyricsData;
              _lyricsLoaded = true;
            });
          }
          return;
        }
      }
    } catch (e) {
      logger.e('Failed to fetch lyrics: $e');
      // Fall back to recording's lyrics data
    }

    // Fallback to local lyrics data
    if (mounted) {
      setState(() {
        _lyricsData = widget.recording.lyricsData;
        _lyricsLoaded = true;
      });
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    // Get user profile information from ProfileBloc via DI
    final profileBloc = DI().resolve<ProfileBloc>();
    final user = profileBloc.melodyzeUser;
    final userProfileUrl = user.profilePicUrl.isNullOrEmpty ? null : '${user.profilePicUrl!}?t=${DateTime.now().millisecondsSinceEpoch}';
    final userProfileImageUrl = userProfileUrl ?? "https://melodyze-public-bucket.s3.ap-south-1.amazonaws.com/dp_placeholder_for_no_profile_picture.jpeg";
    final username = 'FT. ${user.username}';

    return MeloScaffold(
      showBackground: false, // Set to false to avoid gradient backgrounds
      showBackButton: true,
      extendBody: true,
      onBackPressed: () {
        Navigator.of(context).pop();
      },
      overlayAction: Positioned(
        bottom: 32,
        width: MediaQuery.sizeOf(context).width,
        child: MeloBottomNavigationBar(
          currentIndex: 2, // Profile tab index
          items: const [AssetPaths.home, AssetPaths.feed, AssetPaths.profile],
          onTap: (index) {
            if (index == 0) {
              // Navigate to Home
              context.router.popUntilRoot();
            } else if (index == 1) {
              // Navigate to Feed
              context.router.popUntilRoot();
              // You might need to set the tab to feed
            } else if (index == 2) {
              // Stay on profile or navigate back to profile
              Navigator.of(context).pop();
            }
          },
        ),
      ),
      secondaryAction: (context) => PopupMenuButton<String>(
        icon: ShaderMask(
          shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
          child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
        ),
        onSelected: (value) async {
          // Handle menu actions here
          if (value == 'share') {
            // Handle share action
          } else if (value == 'download') {
            // Handle download action
          }
        },
        color: Colors.transparent,
        itemBuilder: (BuildContext context) => [
          PopupMenuItem(
            padding: EdgeInsets.zero,
            child: AppGradientContainer(
              gradient: AppGradients.gradientBlackTeal,
              child: Column(
                children: [
                  PopupMenuItem(
                    value: 'share',
                    child: ListTile(
                      leading: Icon(Icons.share, color: Colors.white),
                      title: Text(
                        'Share',
                        style: AppTextStyles.text20regular.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  PopupMenuItem(
                    value: 'download',
                    child: ListTile(
                      leading: Icon(Icons.download, color: Colors.white),
                      title: Text(
                        'Download',
                        style: AppTextStyles.text20regular.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: ColoredBox(
        color: Colors.black, // Force pure black background
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: _lyricsLoaded
                    ? PracticeRecordingPlayerBody(
                        recording: widget.recording,
                        lyrics: lyricsList,
                        lyricsData: _lyricsData?.lyrics,
                        playbackPositionMillis: _playbackPositionMillis,
                        audioDurationMillis: _audioDurationMillis,
                        isPlaying: _isPlaying,
                        onPlayPause: _togglePlay,
                        userProfileImageUrl: userProfileImageUrl,
                        username: username,
                      )
                    : const Center(
                        child: CircularProgressIndicator(
                          color: Colors.purpleAccent,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ), // Close Container
    ); // Close MeloScaffold
  }
}

class WaveGifPlayer extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onPlayPause;

  const WaveGifPlayer({
    super.key,
    required this.isPlaying,
    required this.onPlayPause,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPlayPause, // Tap anywhere to play/pause
      child: SizedBox(
        height: 120,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (isPlaying)
              Image.asset(
                'assets/gif/wave_video.gif',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 120,
              )
            else
              Image.asset(
                'assets/png/wave_static.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 120,
              ),
          ],
        ),
      ),
    );
  }
}

class _LyricsDisplay extends StatefulWidget {
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;

  const _LyricsDisplay({
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
  });

  @override
  State<_LyricsDisplay> createState() => _LyricsDisplayState();
}

class _LyricsDisplayState extends State<_LyricsDisplay> {
  int _parseTimeToMillis(String time) {
    final parts = time.split(':');
    if (parts.length == 3) {
      final min = int.tryParse(parts[0]) ?? 0;
      final sec = int.tryParse(parts[1]) ?? 0;
      final ms = int.tryParse(parts[2]) ?? 0;
      return min * 60000 + sec * 1000 + ms;
    }
    return 0;
  }

  int _getCurrentLyricIndex() {
    if (widget.lyricsData == null || widget.lyrics.isEmpty) return 0;

    final data = widget.lyricsData!.data;
    if (data.isEmpty) return 0;

    // Handle looping: get position within the lyrics duration
    int adjustedPosition = widget.playbackPositionMillis;
    if (widget.audioDurationMillis > 0) {
      // Calculate lyrics duration
      final lastLyricEnd = data.isNotEmpty ? _parseTimeToMillis(data.last.endTime) : 0;
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;

      // Get position within one loop cycle
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    // Find current lyric based on timing
    for (int i = 0; i < data.length; i++) {
      final start = _parseTimeToMillis(data[i].startTime);
      final end = _parseTimeToMillis(data[i].endTime);

      if (end == 0) {
        // No end time specified, check if we're past start
        if (adjustedPosition >= start) {
          // Check if this is the last lyric or if next lyric hasn't started
          if (i == data.length - 1) return i;
          final nextStart = _parseTimeToMillis(data[i + 1].startTime);
          if (adjustedPosition < nextStart) return i;
        }
      } else {
        // End time specified
        if (adjustedPosition >= start && adjustedPosition < end) {
          return i;
        }
      }
    }

    // Default fallback
    return adjustedPosition < _parseTimeToMillis(data[0].startTime) ? 0 : data.length - 1;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lyrics.isEmpty) {
      return const Center(
        child: Text(
          'No lyrics available',
          style: TextStyle(color: Colors.grey, fontSize: 16),
        ),
      );
    }

    final currentIndex = _getCurrentLyricIndex();

    return SizedBox(
      height: 220,
      width: 280,
      child: Stack(
        children: [
          // Main lyrics column
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Previous lyric
              _buildLyricLine(
                text: currentIndex > 0 ? widget.lyrics[currentIndex - 1] : '',
                isCurrent: false,
                isVisible: currentIndex > 0,
                applyMask: false, // No mask in text itself
              ),

              // Current lyric (highlighted)
              _buildLyricLine(
                text: widget.lyrics[currentIndex],
                isCurrent: true,
                isVisible: true,
                applyMask: false, // No mask for current lyric
              ),

              // Next lyric
              _buildLyricLine(
                text: currentIndex < widget.lyrics.length - 1 ? widget.lyrics[currentIndex + 1] : '',
                isCurrent: false,
                isVisible: currentIndex < widget.lyrics.length - 1,
                applyMask: false, // No mask in text itself
              ),
            ],
          ),

          // Top black mask overlay - positioned over the top lyric
          if (currentIndex > 0)
            Positioned(
              top: 20,
              left: 0,
              right: 0,
              height: 120, // Increased height of one lyric line mask
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

          // Bottom black mask overlay - positioned over the bottom lyric
          if (currentIndex < widget.lyrics.length - 1)
            Positioned(
              bottom: 30,
              left: 0,
              right: 0,
              height: 100, // Increased height of one lyric line mask
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLyricLine({
    required String text,
    required bool isCurrent,
    required bool isVisible,
    bool applyMask = false, // Add this parameter but we won't use it anymore
  }) {
    if (!isVisible || text.isEmpty) {
      return const SizedBox(height: 72); // Maintain spacing (increased to match mask height)
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Center(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Ethnocentric',
            fontSize: isCurrent ? 22 : 16,
            color: isCurrent ? const Color(0xFFFFBBFF) : Colors.white,
            fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
            fontFamilyFallback: const ['Roboto', 'Arial'],
            shadows: isCurrent
                ? [
                    const Shadow(color: Color(0xFFFFBBFF), blurRadius: 0),
                    const Shadow(color: Color(0xFFFFBBFF), blurRadius: 10),
                  ]
                : null,
          ),
        ),
      ),
    );
  }
}
